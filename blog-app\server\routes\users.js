const express = require('express');
const { body, validationResult } = require('express-validator');
const { runQuery, getRow, getAllRows } = require('../config/database');
const { authenticate, checkOwnership } = require('../middleware/auth');
const { AppError, catchAsync } = require('../middleware/errorHandler');
const logger = require('../utils/logger');

const router = express.Router();

// All routes require authentication
router.use(authenticate);

// Get user profile
router.get('/profile', catchAsync(async (req, res, next) => {
  const { password_hash, ...userWithoutPassword } = req.user;
  
  res.status(200).json({
    status: 'success',
    user: userWithoutPassword
  });
}));

// Update user profile
router.put('/profile', [
  body('firstName').optional().trim().isLength({ min: 2, max: 50 }),
  body('lastName').optional().trim().isLength({ min: 2, max: 50 }),
  body('email').optional().isEmail().normalizeEmail()
], catchAsync(async (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      status: 'fail',
      message: 'Validation failed',
      errors: errors.array()
    });
  }

  const { firstName, lastName, email } = req.body;
  const updates = [];
  const values = [];

  if (firstName) {
    updates.push('first_name = ?');
    values.push(firstName);
  }
  if (lastName) {
    updates.push('last_name = ?');
    values.push(lastName);
  }
  if (email) {
    // Check if email is already taken
    const existingUser = await getRow('SELECT id FROM users WHERE email = ? AND id != ?', [email, req.user.id]);
    if (existingUser) {
      return next(new AppError('Email is already taken', 400));
    }
    updates.push('email = ?');
    values.push(email);
  }

  if (updates.length === 0) {
    return next(new AppError('No valid fields to update', 400));
  }

  updates.push('updated_at = CURRENT_TIMESTAMP');
  values.push(req.user.id);

  await runQuery(
    `UPDATE users SET ${updates.join(', ')} WHERE id = ?`,
    values
  );

  // Get updated user
  const updatedUser = await getRow('SELECT * FROM users WHERE id = ?', [req.user.id]);
  const { password_hash, ...userWithoutPassword } = updatedUser;

  logger.info(`User profile updated: ${req.user.email}`);

  res.status(200).json({
    status: 'success',
    message: 'Profile updated successfully',
    user: userWithoutPassword
  });
}));

// Get reading history
router.get('/history', catchAsync(async (req, res, next) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 10;
  const offset = (page - 1) * limit;

  const history = await getAllRows(`
    SELECT 
      ub.*,
      b.title,
      b.author,
      b.cover_image_path,
      b.page_count
    FROM user_books ub
    JOIN books b ON ub.book_id = b.id
    WHERE ub.user_id = ? AND ub.last_read_at IS NOT NULL
    ORDER BY ub.last_read_at DESC
    LIMIT ? OFFSET ?
  `, [req.user.id, limit, offset]);

  // Get total count
  const totalResult = await getRow(`
    SELECT COUNT(*) as total 
    FROM user_books 
    WHERE user_id = ? AND last_read_at IS NOT NULL
  `, [req.user.id]);

  res.status(200).json({
    status: 'success',
    data: {
      history,
      pagination: {
        page,
        limit,
        total: totalResult.total,
        pages: Math.ceil(totalResult.total / limit)
      }
    }
  });
}));

// Get favorite books
router.get('/favorites', catchAsync(async (req, res, next) => {
  const favorites = await getAllRows(`
    SELECT 
      ub.*,
      b.title,
      b.author,
      b.description,
      b.cover_image_path,
      b.category,
      b.page_count
    FROM user_books ub
    JOIN books b ON ub.book_id = b.id
    WHERE ub.user_id = ? AND ub.is_favorite = 1
    ORDER BY ub.last_read_at DESC
  `, [req.user.id]);

  res.status(200).json({
    status: 'success',
    data: {
      favorites
    }
  });
}));

// Add book to favorites
router.post('/favorites/:bookId', catchAsync(async (req, res, next) => {
  const bookId = parseInt(req.params.bookId);

  // Check if book exists
  const book = await getRow('SELECT id FROM books WHERE id = ? AND is_active = 1', [bookId]);
  if (!book) {
    return next(new AppError('Book not found', 404));
  }

  // Insert or update user_books record
  await runQuery(`
    INSERT INTO user_books (user_id, book_id, is_favorite)
    VALUES (?, ?, 1)
    ON CONFLICT(user_id, book_id) 
    DO UPDATE SET is_favorite = 1
  `, [req.user.id, bookId]);

  res.status(200).json({
    status: 'success',
    message: 'Book added to favorites'
  });
}));

// Remove book from favorites
router.delete('/favorites/:bookId', catchAsync(async (req, res, next) => {
  const bookId = parseInt(req.params.bookId);

  await runQuery(`
    UPDATE user_books 
    SET is_favorite = 0 
    WHERE user_id = ? AND book_id = ?
  `, [req.user.id, bookId]);

  res.status(200).json({
    status: 'success',
    message: 'Book removed from favorites'
  });
}));

// Get user statistics
router.get('/stats', catchAsync(async (req, res, next) => {
  const stats = await getRow(`
    SELECT
      COUNT(CASE WHEN downloaded_at IS NOT NULL THEN 1 END) as downloaded_books,
      COUNT(CASE WHEN last_read_at IS NOT NULL THEN 1 END) as read_books,
      COUNT(CASE WHEN is_favorite = 1 THEN 1 END) as favorite_books,
      AVG(reading_progress) as avg_progress
    FROM user_books
    WHERE user_id = ?
  `, [req.user.id]);

  const annotationsCount = await getRow(`
    SELECT COUNT(*) as total_annotations
    FROM annotations
    WHERE user_id = ?
  `, [req.user.id]);

  res.status(200).json({
    status: 'success',
    data: {
      ...stats,
      total_annotations: annotationsCount.total_annotations
    }
  });
}));

// Logout user (client-side token removal)
router.post('/logout', catchAsync(async (req, res, next) => {
  logger.info(`User logged out: ${req.user.email}`);

  res.status(200).json({
    status: 'success',
    message: 'Logout successful'
  });
}));

module.exports = router;
