{
  message: 'Starting database initialization...',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 18:45:55'
}
{
  message: 'Connected to SQLite database: C:\\Users\\<USER>\\Desktop\\apps\\pdf app\\blog-app\\server\\database.sqlite',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 18:45:55'
}
{
  message: 'All database tables and indexes created successfully',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 18:45:57'
}
{
  message: 'Starting database seeding...',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 18:46:06'
}
{
  service: 'pdf-reader-api',
  level: 'error',
  message: 'Database seeding failed: Database not initialized',
  stack: 'Error: Database not initialized\n' +
    '    at C:\\Users\\<USER>\\Desktop\\apps\\pdf app\\blog-app\\server\\config\\database.js:162:14\n' +
    '    at new Promise (<anonymous>)\n' +
    '    at runQuery (C:\\Users\\<USER>\\Desktop\\apps\\pdf app\\blog-app\\server\\config\\database.js:160:10)\n' +
    '    at seedDatabase (C:\\Users\\<USER>\\Desktop\\apps\\pdf app\\blog-app\\server\\scripts\\seedDb.js:11:11)',
  timestamp: '2025-06-13 18:46:07'
}
{
  message: 'Starting database seeding...',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 18:46:29'
}
{
  message: 'Connected to SQLite database: C:\\Users\\<USER>\\Desktop\\apps\\pdf app\\blog-app\\server\\database.sqlite',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 18:46:29'
}
{
  message: 'All database tables and indexes created successfully',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 18:46:29'
}
{
  message: 'Database initialization completed',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 18:46:29'
}
{
  message: 'Database seeding completed successfully!',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 18:46:34'
}
{
  message: 'Connected to SQLite database: C:\\Users\\<USER>\\Desktop\\apps\\pdf app\\blog-app\\server\\database.sqlite',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 18:49:14'
}
{
  message: 'All database tables and indexes created successfully',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 18:49:14'
}
{
  message: 'Database initialization completed',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 18:49:14'
}
{
  message: 'Database initialized successfully',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 18:49:14'
}
{
  message: 'Server running on port 3000',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 18:49:14'
}
{
  message: 'Environment: development',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 18:49:14'
}
{
  message: 'Health check: http://localhost:3000/health',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 18:49:14'
}
{
  message: 'API docs: http://localhost:3000/api/docs',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 18:49:14'
}
{
  message: '::1 - - [13/Jun/2025:15:49:35 +0000] "GET /health HTTP/1.1" 200 102 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.22000.65"',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 18:49:35'
}
{
  message: '::1 - - [13/Jun/2025:15:49:44 +0000] "GET /health HTTP/1.1" 200 102 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 18:49:44'
}
{
  message: '::1 - - [13/Jun/2025:15:49:46 +0000] "GET /api/docs HTTP/1.1" 200 718 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.22000.65"',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 18:49:46'
}
{
  message: '::1 - - [13/Jun/2025:15:49:46 +0000] "GET /favicon.ico HTTP/1.1" 404 101 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 18:49:46'
}
{
  message: 'Connected to SQLite database: ./database.sqlite',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:02:38'
}
{
  message: 'All database tables and indexes created successfully',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:02:39'
}
{
  message: 'Database initialization completed',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:02:39'
}
{
  message: 'Database initialized successfully',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:02:39'
}
{
  message: 'Server running on port 3000',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:02:39'
}
{
  message: 'Environment: development',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:02:39'
}
{
  message: 'Health check: http://localhost:3000/health',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:02:39'
}
{
  message: 'API docs: http://localhost:3000/api/docs',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:02:39'
}
{
  message: 'Created upload directory: C:\\Users\\<USER>\\Desktop\\apps\\pdf app\\blog-app\\server\\uploads\\pdfs',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:13:35'
}
{
  message: 'Connected to SQLite database: ./database.sqlite',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:13:35'
}
{
  message: 'Created upload directory: C:\\Users\\<USER>\\Desktop\\apps\\pdf app\\blog-app\\server\\uploads\\covers',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:13:35'
}
{
  message: 'Created upload directory: C:\\Users\\<USER>\\Desktop\\apps\\pdf app\\blog-app\\server\\uploads\\temp',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:13:35'
}
{
  message: 'All database tables and indexes created successfully',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:13:35'
}
{
  message: 'Database initialization completed',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:13:35'
}
{
  message: 'Database initialized successfully',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:13:35'
}
{
  message: 'Server running on port 3000',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:13:35'
}
{
  message: 'Environment: development',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:13:35'
}
{
  message: 'Health check: http://localhost:3000/health',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:13:35'
}
{
  message: 'API docs: http://localhost:3000/api/docs',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:13:35'
}
{
  message: 'Connected to SQLite database: ./database.sqlite',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:13:53'
}
{
  message: 'All database tables and indexes created successfully',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:13:53'
}
{
  message: 'Database initialization completed',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:13:53'
}
{
  message: 'Database initialized successfully',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:13:53'
}
{
  message: 'Server running on port 3000',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:13:53'
}
{
  message: 'Environment: development',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:13:53'
}
{
  message: 'Health check: http://localhost:3000/health',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:13:53'
}
{
  message: 'API docs: http://localhost:3000/api/docs',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:13:53'
}
{
  message: 'Connected to SQLite database: ./database.sqlite',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:15:26'
}
{
  message: 'All database tables and indexes created successfully',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:15:26'
}
{
  message: 'Database initialization completed',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:15:26'
}
{
  message: 'Database initialized successfully',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:15:26'
}
{
  message: 'Server running on port 3000',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:15:26'
}
{
  message: 'Environment: development',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:15:26'
}
{
  message: 'Health check: http://localhost:3000/health',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:15:26'
}
{
  message: 'API docs: http://localhost:3000/api/docs',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:15:26'
}
{
  message: 'Connected to SQLite database: ./database.sqlite',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:16:37'
}
{
  message: 'All database tables and indexes created successfully',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:16:37'
}
{
  message: 'Database initialization completed',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:16:37'
}
{
  message: 'Database initialized successfully',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:16:37'
}
{
  message: 'Server running on port 3000',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:16:37'
}
{
  message: 'Environment: development',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:16:37'
}
{
  message: 'Health check: http://localhost:3000/health',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:16:37'
}
{
  message: 'API docs: http://localhost:3000/api/docs',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:16:37'
}
{
  message: '::1 - - [13/Jun/2025:16:16:48 +0000] "GET /api/docs HTTP/1.1" 200 718 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:16:48'
}
{
  message: '::1 - - [13/Jun/2025:16:16:49 +0000] "GET /favicon.ico HTTP/1.1" 404 101 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:16:49'
}
{
  message: '::1 - - [13/Jun/2025:16:18:20 +0000] "GET /api/docs HTTP/1.1" 304 - "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:18:20'
}
{
  message: '::1 - - [13/Jun/2025:16:29:19 +0000] "GET /api/docs HTTP/1.1" 304 - "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'pdf-reader-api',
  timestamp: '2025-06-13 19:29:19'
}
