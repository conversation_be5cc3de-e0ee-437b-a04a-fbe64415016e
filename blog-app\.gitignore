# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage (https://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons (https://nodejs.org/api/addons.html)
build/Release

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variable files
.env
.env.development.local
.env.test.local
.env.production.local
.env.local

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next
out

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Vite build output
dist/
dist-ssr/

# Rollup build output
build/

# Webpack build output
bundle/

# Storybook build outputs
.out
.storybook-out
storybook-static

# Temporary folders
tmp/
temp/

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage
*.lcov

# Logs
logs
*.log

# Database
*.sqlite
*.sqlite3
*.db

# Uploaded files
uploads/
storage/local/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# React Native
# Expo
.expo/
.expo-shared/
web-build/

# Android
*.apk
*.aab
android/app/build/
android/build/
android/.gradle/
android/local.properties
android/app/release/

# iOS
ios/build/
ios/Pods/
ios/*.xcworkspace
ios/*.xcuserdata
*.ipa

# macOS
*.dSYM.zip
*.dSYM

# Fastlane
fastlane/report.xml
fastlane/Preview.html
fastlane/screenshots
fastlane/test_output

# Bundle artifacts
*.jsbundle

# CocoaPods
ios/Pods/

# Testing
coverage/
.nyc_output/

# Misc
*.tgz
*.tar.gz

# Local development
.env.local
.env.development.local
.env.test.local
.env.production.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Backup files
*.bak
*.backup
*.old

# PDF files (for development - remove in production)
*.pdf

# Payment API keys and secrets
stripe_keys.txt
mpesa_credentials.txt

# SSL certificates
*.pem
*.key
*.crt

# Docker
.dockerignore
Dockerfile
docker-compose.yml
