# PDF Reader App - Technical Specifications

## Architecture Overview

### System Architecture
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Client    │    │  Mobile Client  │    │  Admin Panel    │
│   (React)       │    │ (React Native)  │    │   (React)       │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────┴───────────┐
                    │     REST API Server     │
                    │    (Node.js/Express)    │
                    └─────────────┬───────────┘
                                 │
                    ┌─────────────┴───────────┐
                    │      Database          │
                    │  (SQLite/PostgreSQL)   │
                    └─────────────────────────┘
```

---

## Database Design

### Core Tables

#### Users Table
```sql
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHA<PERSON>(100),
    role ENUM('guest', 'user', 'subscriber', 'admin', 'editor') DEFAULT 'user',
    subscription_status ENUM('free', 'active', 'expired') DEFAULT 'free',
    subscription_end_date DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_login DATETIME,
    is_active BOOLEAN DEFAULT true
);
```

#### Books Table
```sql
CREATE TABLE books (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title VARCHAR(255) NOT NULL,
    author VARCHAR(255),
    description TEXT,
    file_path VARCHAR(500) NOT NULL,
    file_size INTEGER,
    page_count INTEGER,
    access_level ENUM('public', 'registered', 'paid') DEFAULT 'public',
    price DECIMAL(10,2) DEFAULT 0.00,
    category VARCHAR(100),
    tags TEXT, -- JSON array of tags
    cover_image_path VARCHAR(500),
    upload_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    uploaded_by INTEGER REFERENCES users(id),
    download_count INTEGER DEFAULT 0,
    view_count INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    metadata TEXT -- JSON for additional metadata
);
```

#### User_Books Table (Reading History)
```sql
CREATE TABLE user_books (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER REFERENCES users(id),
    book_id INTEGER REFERENCES books(id),
    downloaded_at DATETIME,
    last_read_at DATETIME,
    current_page INTEGER DEFAULT 1,
    reading_progress DECIMAL(5,2) DEFAULT 0.00, -- percentage
    is_favorite BOOLEAN DEFAULT false,
    UNIQUE(user_id, book_id)
);
```

#### Annotations Table
```sql
CREATE TABLE annotations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER REFERENCES users(id),
    book_id INTEGER REFERENCES books(id),
    page_number INTEGER NOT NULL,
    annotation_type ENUM('highlight', 'note', 'bookmark') NOT NULL,
    content TEXT,
    position_data TEXT, -- JSON for coordinates/selection data
    color VARCHAR(7) DEFAULT '#FFFF00', -- hex color for highlights
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### Payments Table
```sql
CREATE TABLE payments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER REFERENCES users(id),
    book_id INTEGER REFERENCES books(id), -- NULL for subscriptions
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    payment_method ENUM('stripe', 'mpesa', 'google_play', 'apple_iap'),
    transaction_id VARCHAR(255) UNIQUE,
    status ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'pending',
    payment_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    metadata TEXT -- JSON for payment provider specific data
);
```

---

## API Endpoints Specification

### Authentication Endpoints
```
POST   /api/auth/register          # User registration
POST   /api/auth/login             # User login
POST   /api/auth/logout            # User logout
POST   /api/auth/refresh           # Refresh JWT token
POST   /api/auth/forgot-password   # Password reset request
POST   /api/auth/reset-password    # Password reset confirmation
```

### User Endpoints
```
GET    /api/users/profile          # Get user profile
PUT    /api/users/profile          # Update user profile
GET    /api/users/history          # Get reading history
GET    /api/users/favorites        # Get favorite books
POST   /api/users/favorites/:id    # Add book to favorites
DELETE /api/users/favorites/:id    # Remove from favorites
```

### Books Endpoints
```
GET    /api/books                  # List books (with filters)
GET    /api/books/:id              # Get book details
GET    /api/books/:id/download     # Download PDF file
POST   /api/books/search           # Search books
GET    /api/books/:id/annotations  # Get user annotations for book
POST   /api/books/:id/annotations  # Save annotation
PUT    /api/books/:id/annotations/:annotationId  # Update annotation
DELETE /api/books/:id/annotations/:annotationId  # Delete annotation
```

### Admin Endpoints
```
POST   /api/admin/books            # Upload new book
PUT    /api/admin/books/:id        # Update book metadata
DELETE /api/admin/books/:id        # Delete book
GET    /api/admin/analytics        # Get analytics data
GET    /api/admin/users            # List all users
PUT    /api/admin/users/:id        # Update user role/status
GET    /api/admin/payments         # View payment history
```

### Payment Endpoints
```
POST   /api/payments/stripe        # Process Stripe payment
POST   /api/payments/mpesa         # Process M-Pesa payment
POST   /api/payments/verify        # Verify payment status
GET    /api/payments/history       # Get user payment history
```

---

## Frontend Component Structure

### Web Application (React)
```
src/
├── components/
│   ├── common/
│   │   ├── Header.jsx
│   │   ├── Footer.jsx
│   │   ├── LoadingSpinner.jsx
│   │   └── ErrorBoundary.jsx
│   ├── auth/
│   │   ├── LoginForm.jsx
│   │   ├── RegisterForm.jsx
│   │   └── ProtectedRoute.jsx
│   ├── books/
│   │   ├── BookList.jsx
│   │   ├── BookCard.jsx
│   │   ├── BookDetails.jsx
│   │   └── BookSearch.jsx
│   ├── pdf/
│   │   ├── PDFViewer.jsx
│   │   ├── PDFControls.jsx
│   │   ├── AnnotationTools.jsx
│   │   └── BookmarkPanel.jsx
│   └── user/
│       ├── Profile.jsx
│       ├── Library.jsx
│       └── ReadingHistory.jsx
├── pages/
│   ├── Home.jsx
│   ├── Login.jsx
│   ├── Register.jsx
│   ├── BookLibrary.jsx
│   ├── BookReader.jsx
│   └── UserDashboard.jsx
├── services/
│   ├── api.js
│   ├── auth.js
│   └── storage.js
├── hooks/
│   ├── useAuth.js
│   ├── useBooks.js
│   └── usePDF.js
└── utils/
    ├── constants.js
    ├── helpers.js
    └── validators.js
```

### Mobile Application (React Native)
```
src/
├── components/
│   ├── common/
│   ├── auth/
│   ├── books/
│   ├── pdf/
│   └── user/
├── screens/
│   ├── AuthStack/
│   ├── MainStack/
│   └── BookReader/
├── navigation/
│   ├── AppNavigator.js
│   ├── AuthNavigator.js
│   └── TabNavigator.js
├── services/
├── hooks/
└── utils/
```

---

## Security Specifications

### Authentication & Authorization
- JWT tokens with 24-hour expiration
- Refresh tokens with 7-day expiration
- Password hashing using bcrypt (12 rounds)
- Role-based access control (RBAC)
- Rate limiting on authentication endpoints

### File Security
- PDF file validation before upload
- Virus scanning for uploaded files
- Secure file serving with access control
- File path obfuscation to prevent direct access

### API Security
- CORS configuration for allowed origins
- Request rate limiting (100 requests/minute per IP)
- Input validation and sanitization
- SQL injection prevention using parameterized queries
- XSS protection with content security policy

---

## Performance Requirements

### Response Times
- API endpoints: < 200ms average
- PDF loading: < 3 seconds for files up to 50MB
- Search results: < 1 second
- Page navigation: < 100ms

### Scalability Targets
- Support 1000+ concurrent users
- Handle 10GB+ of PDF storage
- Process 100+ file uploads per day
- Support 10,000+ registered users

### Mobile Performance
- App startup time: < 3 seconds
- PDF rendering: < 2 seconds per page
- Offline reading capability
- Efficient memory usage for large PDFs

---

## Integration Specifications

### Payment Gateways

#### Stripe Integration
```javascript
// Stripe configuration
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);

// Payment intent creation
const paymentIntent = await stripe.paymentIntents.create({
  amount: amount * 100, // Convert to cents
  currency: 'usd',
  metadata: { bookId, userId }
});
```

#### M-Pesa Integration
```javascript
// M-Pesa configuration
const mpesa = require('mpesa-node-sdk');

const credentials = {
  consumerKey: process.env.MPESA_CONSUMER_KEY,
  consumerSecret: process.env.MPESA_CONSUMER_SECRET,
  environment: 'sandbox' // or 'production'
};
```

### File Storage

#### Local Development
```javascript
// Multer configuration for local storage
const storage = multer.diskStorage({
  destination: './uploads/pdfs/',
  filename: (req, file, cb) => {
    const uniqueName = `${Date.now()}-${Math.round(Math.random() * 1E9)}.pdf`;
    cb(null, uniqueName);
  }
});
```

#### Production (AWS S3)
```javascript
// AWS S3 configuration
const AWS = require('aws-sdk');
const s3 = new AWS.S3({
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  region: process.env.AWS_REGION
});
```

---

## Environment Configuration

### Development Environment
```env
NODE_ENV=development
PORT=3000
DB_TYPE=sqlite
DB_PATH=./database.sqlite
JWT_SECRET=your-jwt-secret-key
JWT_REFRESH_SECRET=your-refresh-secret-key
UPLOAD_PATH=./uploads
```

### Production Environment
```env
NODE_ENV=production
PORT=3000
DATABASE_URL=postgresql://user:password@host:port/database
JWT_SECRET=production-jwt-secret
JWT_REFRESH_SECRET=production-refresh-secret
AWS_ACCESS_KEY_ID=your-aws-key
AWS_SECRET_ACCESS_KEY=your-aws-secret
AWS_REGION=us-east-1
S3_BUCKET_NAME=pdf-reader-storage
STRIPE_SECRET_KEY=sk_live_...
MPESA_CONSUMER_KEY=your-mpesa-key
MPESA_CONSUMER_SECRET=your-mpesa-secret
```

---

## Testing Strategy

### Unit Testing
- Backend: Jest + Supertest
- Frontend: Jest + React Testing Library
- Mobile: Jest + React Native Testing Library

### Integration Testing
- API endpoint testing
- Database operation testing
- File upload/download testing
- Payment flow testing

### End-to-End Testing
- User registration and login flows
- PDF viewing and annotation workflows
- Payment processing workflows
- Admin panel operations

---

## Deployment Architecture

### Development
- Local SQLite database
- Local file storage
- Development API keys

### Staging
- PostgreSQL database
- AWS S3 storage
- Sandbox payment APIs

### Production
- Managed PostgreSQL (AWS RDS)
- AWS S3 with CloudFront CDN
- Production payment APIs
- Load balancer for high availability

---

**Last Updated**: [Current Date]
**Version**: 1.0
