const multer = require('multer');
const path = require('path');
const fs = require('fs').promises;
const { AppError } = require('./errorHandler');
const logger = require('../utils/logger');

// Ensure upload directories exist
const ensureUploadDirs = async () => {
  const uploadDirs = [
    path.join(__dirname, '../uploads'),
    path.join(__dirname, '../uploads/pdfs'),
    path.join(__dirname, '../uploads/covers'),
    path.join(__dirname, '../uploads/temp')
  ];

  for (const dir of uploadDirs) {
    try {
      await fs.access(dir);
    } catch (error) {
      await fs.mkdir(dir, { recursive: true });
      logger.info(`Created upload directory: ${dir}`);
    }
  }
};

// Initialize upload directories
ensureUploadDirs().catch(error => {
  logger.error('Failed to create upload directories:', error);
});

// Storage configuration for PDFs
const pdfStorage = multer.diskStorage({
  destination: async (req, file, cb) => {
    const uploadPath = path.join(__dirname, '../uploads/pdfs');
    cb(null, uploadPath);
  },
  filename: (req, file, cb) => {
    // Generate unique filename with timestamp and random string
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const filename = `pdf-${uniqueSuffix}.pdf`;
    cb(null, filename);
  }
});

// Storage configuration for cover images
const coverStorage = multer.diskStorage({
  destination: async (req, file, cb) => {
    const uploadPath = path.join(__dirname, '../uploads/covers');
    cb(null, uploadPath);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    const filename = `cover-${uniqueSuffix}${ext}`;
    cb(null, filename);
  }
});

// File filter for PDFs
const pdfFileFilter = (req, file, cb) => {
  if (file.mimetype === 'application/pdf') {
    cb(null, true);
  } else {
    cb(new AppError('Only PDF files are allowed', 400), false);
  }
};

// File filter for images (cover images)
const imageFileFilter = (req, file, cb) => {
  const allowedMimes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
  if (allowedMimes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new AppError('Only JPEG, PNG, and WebP images are allowed', 400), false);
  }
};

// PDF upload configuration
const uploadPDF = multer({
  storage: pdfStorage,
  fileFilter: pdfFileFilter,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE) || 50 * 1024 * 1024, // 50MB default
    files: 1
  }
});

// Cover image upload configuration
const uploadCover = multer({
  storage: coverStorage,
  fileFilter: imageFileFilter,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB for images
    files: 1
  }
});

// Multiple file upload (PDF + cover)
const uploadBookFiles = multer({
  storage: multer.diskStorage({
    destination: (req, file, cb) => {
      if (file.fieldname === 'pdf') {
        cb(null, path.join(__dirname, '../uploads/pdfs'));
      } else if (file.fieldname === 'cover') {
        cb(null, path.join(__dirname, '../uploads/covers'));
      } else {
        cb(new AppError('Invalid field name', 400), false);
      }
    },
    filename: (req, file, cb) => {
      const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
      if (file.fieldname === 'pdf') {
        cb(null, `pdf-${uniqueSuffix}.pdf`);
      } else if (file.fieldname === 'cover') {
        const ext = path.extname(file.originalname);
        cb(null, `cover-${uniqueSuffix}${ext}`);
      }
    }
  }),
  fileFilter: (req, file, cb) => {
    if (file.fieldname === 'pdf') {
      pdfFileFilter(req, file, cb);
    } else if (file.fieldname === 'cover') {
      imageFileFilter(req, file, cb);
    } else {
      cb(new AppError('Invalid field name', 400), false);
    }
  },
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB max
    files: 2
  }
});

// Error handling middleware for multer
const handleUploadError = (error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return next(new AppError('File too large', 400));
    }
    if (error.code === 'LIMIT_FILE_COUNT') {
      return next(new AppError('Too many files', 400));
    }
    if (error.code === 'LIMIT_UNEXPECTED_FILE') {
      return next(new AppError('Unexpected file field', 400));
    }
  }
  next(error);
};

// Cleanup function to remove uploaded files on error
const cleanupFiles = async (files) => {
  if (!files) return;
  
  const filesToDelete = Array.isArray(files) ? files : [files];
  
  for (const file of filesToDelete) {
    try {
      await fs.unlink(file.path);
      logger.info(`Cleaned up file: ${file.path}`);
    } catch (error) {
      logger.error(`Failed to cleanup file ${file.path}:`, error);
    }
  }
};

// Middleware to validate uploaded PDF
const validatePDF = async (req, res, next) => {
  if (!req.file && !req.files) {
    return next(new AppError('No file uploaded', 400));
  }

  const pdfFile = req.file || (req.files && req.files.pdf && req.files.pdf[0]);
  
  if (!pdfFile) {
    return next(new AppError('PDF file is required', 400));
  }

  // Additional PDF validation can be added here
  // For now, we rely on mimetype checking in fileFilter
  
  next();
};

module.exports = {
  uploadPDF: uploadPDF.single('pdf'),
  uploadCover: uploadCover.single('cover'),
  uploadBookFiles: uploadBookFiles.fields([
    { name: 'pdf', maxCount: 1 },
    { name: 'cover', maxCount: 1 }
  ]),
  handleUploadError,
  cleanupFiles,
  validatePDF,
  ensureUploadDirs
};
