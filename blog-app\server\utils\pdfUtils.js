const fs = require('fs').promises;
const path = require('path');
const logger = require('./logger');

/**
 * Extract basic metadata from PDF file
 * Note: This is a basic implementation. For production, consider using pdf-parse or pdf2pic
 */
const extractPDFMetadata = async (filePath) => {
  try {
    const stats = await fs.stat(filePath);
    
    // Basic file information
    const metadata = {
      fileSize: stats.size,
      fileName: path.basename(filePath),
      uploadDate: new Date().toISOString(),
      pageCount: null, // Will be estimated or extracted later
      title: null,
      author: null,
      subject: null,
      creator: null,
      producer: null,
      creationDate: null,
      modificationDate: null
    };

    // Try to read PDF header for basic validation
    const buffer = await fs.readFile(filePath);
    
    // Check if it's a valid PDF (starts with %PDF)
    if (!buffer.toString('ascii', 0, 4).startsWith('%PDF')) {
      throw new Error('Invalid PDF file format');
    }

    // Extract PDF version
    const versionMatch = buffer.toString('ascii', 0, 20).match(/%PDF-(\d\.\d)/);
    if (versionMatch) {
      metadata.pdfVersion = versionMatch[1];
    }

    // Estimate page count by counting 'endobj' occurrences
    // This is a rough estimation and may not be 100% accurate
    const content = buffer.toString('binary');
    const pageMatches = content.match(/\/Type\s*\/Page[^s]/g);
    if (pageMatches) {
      metadata.pageCount = pageMatches.length;
    } else {
      // Fallback: count page objects differently
      const objMatches = content.match(/\d+\s+0\s+obj/g);
      metadata.pageCount = objMatches ? Math.max(1, Math.floor(objMatches.length / 10)) : 1;
    }

    // Try to extract title from PDF metadata
    const titleMatch = content.match(/\/Title\s*\(([^)]+)\)/);
    if (titleMatch) {
      metadata.title = titleMatch[1].trim();
    }

    // Try to extract author from PDF metadata
    const authorMatch = content.match(/\/Author\s*\(([^)]+)\)/);
    if (authorMatch) {
      metadata.author = authorMatch[1].trim();
    }

    // Try to extract subject from PDF metadata
    const subjectMatch = content.match(/\/Subject\s*\(([^)]+)\)/);
    if (subjectMatch) {
      metadata.subject = subjectMatch[1].trim();
    }

    // Try to extract creator from PDF metadata
    const creatorMatch = content.match(/\/Creator\s*\(([^)]+)\)/);
    if (creatorMatch) {
      metadata.creator = creatorMatch[1].trim();
    }

    // Try to extract producer from PDF metadata
    const producerMatch = content.match(/\/Producer\s*\(([^)]+)\)/);
    if (producerMatch) {
      metadata.producer = producerMatch[1].trim();
    }

    logger.info(`Extracted metadata for PDF: ${metadata.fileName}`);
    return metadata;

  } catch (error) {
    logger.error(`Failed to extract PDF metadata from ${filePath}:`, error);
    
    // Return basic metadata even if extraction fails
    try {
      const stats = await fs.stat(filePath);
      return {
        fileSize: stats.size,
        fileName: path.basename(filePath),
        uploadDate: new Date().toISOString(),
        pageCount: 1, // Default fallback
        title: null,
        author: null,
        subject: null,
        creator: null,
        producer: null,
        creationDate: null,
        modificationDate: null,
        pdfVersion: null
      };
    } catch (statError) {
      throw new Error(`Failed to read file: ${statError.message}`);
    }
  }
};

/**
 * Validate PDF file integrity
 */
const validatePDFFile = async (filePath) => {
  try {
    const buffer = await fs.readFile(filePath);
    
    // Check minimum file size (1KB)
    if (buffer.length < 1024) {
      return { valid: false, error: 'File too small to be a valid PDF' };
    }

    // Check PDF header
    if (!buffer.toString('ascii', 0, 4).startsWith('%PDF')) {
      return { valid: false, error: 'Invalid PDF header' };
    }

    // Check for PDF trailer
    const content = buffer.toString('binary');
    if (!content.includes('%%EOF')) {
      return { valid: false, error: 'PDF file appears to be corrupted (missing EOF)' };
    }

    return { valid: true };

  } catch (error) {
    return { valid: false, error: `Failed to validate PDF: ${error.message}` };
  }
};

/**
 * Generate thumbnail from PDF (placeholder for future implementation)
 */
const generatePDFThumbnail = async (filePath, outputPath) => {
  // TODO: Implement PDF thumbnail generation using pdf2pic or similar
  // For now, return a placeholder
  logger.info(`Thumbnail generation requested for ${filePath} -> ${outputPath}`);
  return null;
};

/**
 * Get PDF text content for search indexing (placeholder)
 */
const extractPDFText = async (filePath) => {
  // TODO: Implement text extraction using pdf-parse or similar
  // For now, return empty string
  logger.info(`Text extraction requested for ${filePath}`);
  return '';
};

/**
 * Calculate file hash for duplicate detection
 */
const calculateFileHash = async (filePath) => {
  const crypto = require('crypto');
  const buffer = await fs.readFile(filePath);
  return crypto.createHash('sha256').update(buffer).digest('hex');
};

/**
 * Clean filename for safe storage
 */
const sanitizeFilename = (filename) => {
  return filename
    .replace(/[^a-zA-Z0-9.-]/g, '_')
    .replace(/_{2,}/g, '_')
    .replace(/^_+|_+$/g, '')
    .toLowerCase();
};

module.exports = {
  extractPDFMetadata,
  validatePDFFile,
  generatePDFThumbnail,
  extractPDFText,
  calculateFileHash,
  sanitizeFilename
};
