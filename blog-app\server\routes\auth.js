const express = require('express');
const rateLimit = require('express-rate-limit');
const { body, validationResult } = require('express-validator');
const { runQuery, getRow } = require('../config/database');
const { 
  generateToken, 
  generateRefreshToken, 
  verifyToken, 
  hashPassword, 
  comparePassword,
  authenticate
} = require('../middleware/auth');
const { AppError, catchAsync } = require('../middleware/errorHandler');
const logger = require('../utils/logger');

const router = express.Router();

// Rate limiting for auth endpoints
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // limit each IP to 5 requests per windowMs
  message: {
    error: 'Too many authentication attempts',
    message: 'Please try again later'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Validation rules
const registerValidation = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email'),
  body('password')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('Password must contain at least one uppercase letter, one lowercase letter, and one number'),
  body('firstName')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('First name must be between 2 and 50 characters'),
  body('lastName')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Last name must be between 2 and 50 characters')
];

const loginValidation = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email'),
  body('password')
    .notEmpty()
    .withMessage('Password is required')
];

// Helper function to check validation errors
const checkValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      status: 'fail',
      message: 'Validation failed',
      errors: errors.array()
    });
  }
  next();
};

// Helper function to create and send token response
const createSendToken = (user, statusCode, res, message = 'Success') => {
  const token = generateToken({ id: user.id, email: user.email, role: user.role });
  const refreshToken = generateRefreshToken({ id: user.id });

  // Remove password from output
  const { password_hash, ...userWithoutPassword } = user;

  res.status(statusCode).json({
    status: 'success',
    message,
    token,
    refreshToken,
    user: userWithoutPassword
  });
};

// Register new user
router.post('/register', authLimiter, registerValidation, checkValidationErrors, catchAsync(async (req, res, next) => {
  const { email, password, firstName, lastName } = req.body;

  // Check if user already exists
  const existingUser = await getRow('SELECT id FROM users WHERE email = ?', [email]);
  if (existingUser) {
    return next(new AppError('User with this email already exists', 400));
  }

  // Hash password
  const passwordHash = await hashPassword(password);

  // Create user
  const result = await runQuery(
    `INSERT INTO users (email, password_hash, first_name, last_name, role) 
     VALUES (?, ?, ?, ?, ?)`,
    [email, passwordHash, firstName, lastName, 'user']
  );

  // Get created user
  const newUser = await getRow('SELECT * FROM users WHERE id = ?', [result.id]);

  logger.info(`New user registered: ${email}`);
  createSendToken(newUser, 201, res, 'User registered successfully');
}));

// Login user
router.post('/login', authLimiter, loginValidation, checkValidationErrors, catchAsync(async (req, res, next) => {
  const { email, password } = req.body;

  // Check if user exists and is active
  const user = await getRow('SELECT * FROM users WHERE email = ? AND is_active = 1', [email]);
  if (!user) {
    return next(new AppError('Invalid email or password', 401));
  }

  // Check password
  const isPasswordCorrect = await comparePassword(password, user.password_hash);
  if (!isPasswordCorrect) {
    return next(new AppError('Invalid email or password', 401));
  }

  // Update last login
  await runQuery('UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?', [user.id]);

  logger.info(`User logged in: ${email}`);
  createSendToken(user, 200, res, 'Login successful');
}));

// Refresh token
router.post('/refresh', catchAsync(async (req, res, next) => {
  const { refreshToken } = req.body;

  if (!refreshToken) {
    return next(new AppError('Refresh token is required', 400));
  }

  // Verify refresh token
  const decoded = await verifyToken(refreshToken, process.env.JWT_REFRESH_SECRET);

  // Check if user still exists
  const user = await getRow('SELECT * FROM users WHERE id = ? AND is_active = 1', [decoded.id]);
  if (!user) {
    return next(new AppError('User no longer exists', 401));
  }

  // Generate new tokens
  createSendToken(user, 200, res, 'Token refreshed successfully');
}));

// Logout user (client-side token removal)
router.post('/logout', authenticate, catchAsync(async (req, res, next) => {
  logger.info(`User logged out: ${req.user.email}`);
  
  res.status(200).json({
    status: 'success',
    message: 'Logout successful'
  });
}));

// Get current user
router.get('/me', authenticate, catchAsync(async (req, res, next) => {
  const { password_hash, ...userWithoutPassword } = req.user;
  
  res.status(200).json({
    status: 'success',
    user: userWithoutPassword
  });
}));

// Forgot password (placeholder for future implementation)
router.post('/forgot-password', authLimiter, catchAsync(async (req, res, next) => {
  const { email } = req.body;

  if (!email) {
    return next(new AppError('Email is required', 400));
  }

  // Check if user exists
  const user = await getRow('SELECT id, email FROM users WHERE email = ? AND is_active = 1', [email]);
  if (!user) {
    // Don't reveal if email exists or not
    return res.status(200).json({
      status: 'success',
      message: 'If an account with that email exists, a password reset link has been sent'
    });
  }

  // TODO: Implement password reset email functionality
  logger.info(`Password reset requested for: ${email}`);

  res.status(200).json({
    status: 'success',
    message: 'If an account with that email exists, a password reset link has been sent'
  });
}));

// Reset password (placeholder for future implementation)
router.post('/reset-password', authLimiter, catchAsync(async (req, res, next) => {
  const { token, password } = req.body;

  if (!token || !password) {
    return next(new AppError('Token and password are required', 400));
  }

  // TODO: Implement password reset functionality
  res.status(200).json({
    status: 'success',
    message: 'Password reset functionality will be implemented in future version'
  });
}));

module.exports = router;
