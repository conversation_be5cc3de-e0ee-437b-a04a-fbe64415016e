const express = require('express');
const path = require('path');
const fs = require('fs').promises;
const { authenticate, authorize } = require('../middleware/auth');
const { AppError, catchAsync } = require('../middleware/errorHandler');
const { getAllRows, getRow, runQuery } = require('../config/database');
const { uploadBookFiles, handleUploadError, validatePDF, cleanupFiles } = require('../middleware/upload');
const { extractPDFMetadata, validatePDFFile, calculateFileHash } = require('../utils/pdfUtils');
const logger = require('../utils/logger');

const router = express.Router();

// All admin routes require authentication and admin/editor role
router.use(authenticate);
router.use(authorize('admin', 'editor'));

// Get dashboard analytics
router.get('/analytics', catchAsync(async (req, res, next) => {
  // Get basic stats
  const stats = await getRow(`
    SELECT 
      (SELECT COUNT(*) FROM users WHERE is_active = 1) as total_users,
      (SELECT COUNT(*) FROM books WHERE is_active = 1) as total_books,
      (SELECT SUM(download_count) FROM books) as total_downloads,
      (SELECT SUM(view_count) FROM books) as total_views
  `);

  // Get recent registrations (last 30 days)
  const recentUsers = await getRow(`
    SELECT COUNT(*) as recent_users
    FROM users 
    WHERE created_at >= datetime('now', '-30 days')
  `);

  // Get top books by downloads
  const topBooks = await getAllRows(`
    SELECT title, author, download_count, view_count
    FROM books 
    WHERE is_active = 1
    ORDER BY download_count DESC 
    LIMIT 10
  `);

  // Get books by category
  const booksByCategory = await getAllRows(`
    SELECT 
      category,
      COUNT(*) as count
    FROM books 
    WHERE is_active = 1 AND category IS NOT NULL
    GROUP BY category
    ORDER BY count DESC
  `);

  res.status(200).json({
    status: 'success',
    data: {
      stats: {
        ...stats,
        recent_users: recentUsers.recent_users
      },
      topBooks,
      booksByCategory
    }
  });
}));

// Get all users
router.get('/users', catchAsync(async (req, res, next) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 20;
  const offset = (page - 1) * limit;
  const search = req.query.search;

  let whereClause = '';
  let queryParams = [];

  if (search) {
    whereClause = 'WHERE email LIKE ? OR first_name LIKE ? OR last_name LIKE ?';
    const searchTerm = `%${search}%`;
    queryParams.push(searchTerm, searchTerm, searchTerm);
  }

  const users = await getAllRows(`
    SELECT 
      id,
      email,
      first_name,
      last_name,
      role,
      subscription_status,
      subscription_end_date,
      created_at,
      last_login,
      is_active
    FROM users
    ${whereClause}
    ORDER BY created_at DESC
    LIMIT ? OFFSET ?
  `, [...queryParams, limit, offset]);

  // Get total count
  const totalResult = await getRow(`
    SELECT COUNT(*) as total 
    FROM users 
    ${whereClause}
  `, queryParams);

  res.status(200).json({
    status: 'success',
    data: {
      users,
      pagination: {
        page,
        limit,
        total: totalResult.total,
        pages: Math.ceil(totalResult.total / limit)
      }
    }
  });
}));

// Update user role/status
router.put('/users/:id', authorize('admin'), catchAsync(async (req, res, next) => {
  const userId = parseInt(req.params.id);
  const { role, is_active, subscription_status } = req.body;

  // Check if user exists
  const user = await getRow('SELECT * FROM users WHERE id = ?', [userId]);
  if (!user) {
    return next(new AppError('User not found', 404));
  }

  // Prevent admin from deactivating themselves
  if (userId === req.user.id && is_active === false) {
    return next(new AppError('You cannot deactivate your own account', 400));
  }

  const updates = [];
  const values = [];

  if (role && ['user', 'subscriber', 'admin', 'editor'].includes(role)) {
    updates.push('role = ?');
    values.push(role);
  }

  if (typeof is_active === 'boolean') {
    updates.push('is_active = ?');
    values.push(is_active ? 1 : 0);
  }

  if (subscription_status && ['free', 'active', 'expired'].includes(subscription_status)) {
    updates.push('subscription_status = ?');
    values.push(subscription_status);
  }

  if (updates.length === 0) {
    return next(new AppError('No valid fields to update', 400));
  }

  updates.push('updated_at = CURRENT_TIMESTAMP');
  values.push(userId);

  await runQuery(
    `UPDATE users SET ${updates.join(', ')} WHERE id = ?`,
    values
  );

  logger.info(`User updated by admin: ${user.email} by ${req.user.email}`);

  res.status(200).json({
    status: 'success',
    message: 'User updated successfully'
  });
}));

// Get all books for admin management
router.get('/books', catchAsync(async (req, res, next) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 20;
  const offset = (page - 1) * limit;

  const books = await getAllRows(`
    SELECT 
      b.*,
      u.first_name || ' ' || u.last_name as uploaded_by_name
    FROM books b
    LEFT JOIN users u ON b.uploaded_by = u.id
    ORDER BY b.upload_date DESC
    LIMIT ? OFFSET ?
  `, [limit, offset]);

  // Get total count
  const totalResult = await getRow('SELECT COUNT(*) as total FROM books');

  res.status(200).json({
    status: 'success',
    data: {
      books,
      pagination: {
        page,
        limit,
        total: totalResult.total,
        pages: Math.ceil(totalResult.total / limit)
      }
    }
  });
}));

// Upload new book
router.post('/books', uploadBookFiles, handleUploadError, validatePDF, catchAsync(async (req, res, next) => {
  try {
    const { title, author, description, category, access_level, price, tags } = req.body;

    // Validate required fields
    if (!title || !author) {
      await cleanupFiles(req.files);
      return next(new AppError('Title and author are required', 400));
    }

    // Get uploaded files
    const pdfFile = req.files && req.files.pdf && req.files.pdf[0];
    const coverFile = req.files && req.files.cover && req.files.cover[0];

    if (!pdfFile) {
      await cleanupFiles(req.files);
      return next(new AppError('PDF file is required', 400));
    }

    // Validate PDF file
    const validation = await validatePDFFile(pdfFile.path);
    if (!validation.valid) {
      await cleanupFiles(req.files);
      return next(new AppError(validation.error, 400));
    }

    // Extract PDF metadata
    const metadata = await extractPDFMetadata(pdfFile.path);

    // Calculate file hash for duplicate detection
    const fileHash = await calculateFileHash(pdfFile.path);

    // Check for duplicate files
    const existingBook = await getRow('SELECT id, title FROM books WHERE file_hash = ?', [fileHash]);
    if (existingBook) {
      await cleanupFiles(req.files);
      return next(new AppError(`This file already exists as "${existingBook.title}"`, 400));
    }

    // Prepare file paths for database storage
    const pdfPath = `uploads/pdfs/${pdfFile.filename}`;
    const coverPath = coverFile ? `uploads/covers/${coverFile.filename}` : null;

    // Insert book into database
    const result = await runQuery(`
      INSERT INTO books (
        title, author, description, category, access_level, price, tags,
        file_path, cover_image_path, file_size, page_count, file_hash,
        uploaded_by, upload_date, is_active
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, 1)
    `, [
      title.trim(),
      author.trim(),
      description ? description.trim() : null,
      category || 'General',
      access_level || 'public',
      price ? parseFloat(price) : 0,
      tags ? JSON.stringify(Array.isArray(tags) ? tags : [tags]) : null,
      pdfPath,
      coverPath,
      metadata.fileSize,
      metadata.pageCount || 1,
      fileHash,
      req.user.id
    ]);

    logger.info(`Book uploaded successfully: ${title} by ${req.user.email}`);

    res.status(201).json({
      status: 'success',
      message: 'Book uploaded successfully',
      data: {
        bookId: result.lastID,
        title,
        author,
        fileSize: metadata.fileSize,
        pageCount: metadata.pageCount,
        pdfPath,
        coverPath
      }
    });

  } catch (error) {
    // Cleanup uploaded files on error
    await cleanupFiles(req.files);
    throw error;
  }
}));

// Update book metadata
router.put('/books/:id', catchAsync(async (req, res, next) => {
  const bookId = parseInt(req.params.id);
  const { title, author, description, category, access_level, price, tags } = req.body;

  // Check if book exists
  const book = await getRow('SELECT * FROM books WHERE id = ?', [bookId]);
  if (!book) {
    return next(new AppError('Book not found', 404));
  }

  const updates = [];
  const values = [];

  if (title) {
    updates.push('title = ?');
    values.push(title);
  }
  if (author) {
    updates.push('author = ?');
    values.push(author);
  }
  if (description) {
    updates.push('description = ?');
    values.push(description);
  }
  if (category) {
    updates.push('category = ?');
    values.push(category);
  }
  if (access_level && ['public', 'registered', 'paid'].includes(access_level)) {
    updates.push('access_level = ?');
    values.push(access_level);
  }
  if (price !== undefined) {
    updates.push('price = ?');
    values.push(parseFloat(price));
  }
  if (tags) {
    updates.push('tags = ?');
    values.push(JSON.stringify(tags));
  }

  if (updates.length === 0) {
    return next(new AppError('No valid fields to update', 400));
  }

  values.push(bookId);

  await runQuery(
    `UPDATE books SET ${updates.join(', ')} WHERE id = ?`,
    values
  );

  logger.info(`Book updated by admin: ${book.title} by ${req.user.email}`);

  res.status(200).json({
    status: 'success',
    message: 'Book updated successfully'
  });
}));

// Delete book
router.delete('/books/:id', authorize('admin'), catchAsync(async (req, res, next) => {
  const bookId = parseInt(req.params.id);

  // Check if book exists
  const book = await getRow('SELECT * FROM books WHERE id = ?', [bookId]);
  if (!book) {
    return next(new AppError('Book not found', 404));
  }

  // Soft delete (set is_active to false)
  await runQuery('UPDATE books SET is_active = 0 WHERE id = ?', [bookId]);

  logger.info(`Book deleted by admin: ${book.title} by ${req.user.email}`);

  res.status(200).json({
    status: 'success',
    message: 'Book deleted successfully'
  });
}));

// Get all users for admin management
router.get('/users', catchAsync(async (req, res, next) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 20;
  const offset = (page - 1) * limit;
  const search = req.query.search;
  const role = req.query.role;
  const status = req.query.status;

  let whereClause = 'WHERE 1=1';
  let queryParams = [];

  // Add search filter
  if (search) {
    whereClause += ' AND (email LIKE ? OR first_name LIKE ? OR last_name LIKE ?)';
    const searchTerm = `%${search}%`;
    queryParams.push(searchTerm, searchTerm, searchTerm);
  }

  // Add role filter
  if (role && ['admin', 'editor', 'user'].includes(role)) {
    whereClause += ' AND role = ?';
    queryParams.push(role);
  }

  // Add status filter
  if (status) {
    const isActive = status === 'active' ? 1 : 0;
    whereClause += ' AND is_active = ?';
    queryParams.push(isActive);
  }

  const users = await getAllRows(`
    SELECT
      id,
      email,
      first_name,
      last_name,
      role,
      is_active,
      subscription_status,
      subscription_end_date,
      created_at,
      last_login_at,
      (SELECT COUNT(*) FROM user_books WHERE user_id = users.id AND downloaded_at IS NOT NULL) as downloaded_books,
      (SELECT COUNT(*) FROM annotations WHERE user_id = users.id) as total_annotations
    FROM users
    ${whereClause}
    ORDER BY created_at DESC
    LIMIT ? OFFSET ?
  `, [...queryParams, limit, offset]);

  // Get total count
  const totalResult = await getRow(`
    SELECT COUNT(*) as total
    FROM users
    ${whereClause}
  `, queryParams);

  res.status(200).json({
    status: 'success',
    data: {
      users,
      pagination: {
        page,
        limit,
        total: totalResult.total,
        pages: Math.ceil(totalResult.total / limit)
      }
    }
  });
}));

// Update user role or status
router.put('/users/:id', authorize('admin'), catchAsync(async (req, res, next) => {
  const userId = parseInt(req.params.id);
  const { role, is_active, subscription_status, subscription_end_date } = req.body;

  // Check if user exists
  const user = await getRow('SELECT * FROM users WHERE id = ?', [userId]);
  if (!user) {
    return next(new AppError('User not found', 404));
  }

  // Prevent admin from changing their own role
  if (userId === req.user.id && role && role !== req.user.role) {
    return next(new AppError('You cannot change your own role', 400));
  }

  const updates = [];
  const values = [];

  if (role && ['admin', 'editor', 'user'].includes(role)) {
    updates.push('role = ?');
    values.push(role);
  }

  if (is_active !== undefined) {
    updates.push('is_active = ?');
    values.push(is_active ? 1 : 0);
  }

  if (subscription_status && ['active', 'inactive', 'expired'].includes(subscription_status)) {
    updates.push('subscription_status = ?');
    values.push(subscription_status);
  }

  if (subscription_end_date) {
    updates.push('subscription_end_date = ?');
    values.push(subscription_end_date);
  }

  if (updates.length === 0) {
    return next(new AppError('No valid fields to update', 400));
  }

  updates.push('updated_at = CURRENT_TIMESTAMP');
  values.push(userId);

  await runQuery(
    `UPDATE users SET ${updates.join(', ')} WHERE id = ?`,
    values
  );

  logger.info(`User updated by admin: ${user.email} by ${req.user.email}`);

  res.status(200).json({
    status: 'success',
    message: 'User updated successfully'
  });
}));

// Get payment history
router.get('/payments', catchAsync(async (req, res, next) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 20;
  const offset = (page - 1) * limit;

  const payments = await getAllRows(`
    SELECT 
      p.*,
      u.email as user_email,
      u.first_name || ' ' || u.last_name as user_name,
      b.title as book_title
    FROM payments p
    LEFT JOIN users u ON p.user_id = u.id
    LEFT JOIN books b ON p.book_id = b.id
    ORDER BY p.payment_date DESC
    LIMIT ? OFFSET ?
  `, [limit, offset]);

  // Get total count
  const totalResult = await getRow('SELECT COUNT(*) as total FROM payments');

  res.status(200).json({
    status: 'success',
    data: {
      payments,
      pagination: {
        page,
        limit,
        total: totalResult.total,
        pages: Math.ceil(totalResult.total / limit)
      }
    }
  });
}));

module.exports = router;
