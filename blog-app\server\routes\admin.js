const express = require('express');
const { authenticate, authorize } = require('../middleware/auth');
const { AppError, catchAsync } = require('../middleware/errorHandler');
const { getAllRows, getRow, runQuery } = require('../config/database');
const logger = require('../utils/logger');

const router = express.Router();

// All admin routes require authentication and admin/editor role
router.use(authenticate);
router.use(authorize('admin', 'editor'));

// Get dashboard analytics
router.get('/analytics', catchAsync(async (req, res, next) => {
  // Get basic stats
  const stats = await getRow(`
    SELECT 
      (SELECT COUNT(*) FROM users WHERE is_active = 1) as total_users,
      (SELECT COUNT(*) FROM books WHERE is_active = 1) as total_books,
      (SELECT SUM(download_count) FROM books) as total_downloads,
      (SELECT SUM(view_count) FROM books) as total_views
  `);

  // Get recent registrations (last 30 days)
  const recentUsers = await getRow(`
    SELECT COUNT(*) as recent_users
    FROM users 
    WHERE created_at >= datetime('now', '-30 days')
  `);

  // Get top books by downloads
  const topBooks = await getAllRows(`
    SELECT title, author, download_count, view_count
    FROM books 
    WHERE is_active = 1
    ORDER BY download_count DESC 
    LIMIT 10
  `);

  // Get books by category
  const booksByCategory = await getAllRows(`
    SELECT 
      category,
      COUNT(*) as count
    FROM books 
    WHERE is_active = 1 AND category IS NOT NULL
    GROUP BY category
    ORDER BY count DESC
  `);

  res.status(200).json({
    status: 'success',
    data: {
      stats: {
        ...stats,
        recent_users: recentUsers.recent_users
      },
      topBooks,
      booksByCategory
    }
  });
}));

// Get all users
router.get('/users', catchAsync(async (req, res, next) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 20;
  const offset = (page - 1) * limit;
  const search = req.query.search;

  let whereClause = '';
  let queryParams = [];

  if (search) {
    whereClause = 'WHERE email LIKE ? OR first_name LIKE ? OR last_name LIKE ?';
    const searchTerm = `%${search}%`;
    queryParams.push(searchTerm, searchTerm, searchTerm);
  }

  const users = await getAllRows(`
    SELECT 
      id,
      email,
      first_name,
      last_name,
      role,
      subscription_status,
      subscription_end_date,
      created_at,
      last_login,
      is_active
    FROM users
    ${whereClause}
    ORDER BY created_at DESC
    LIMIT ? OFFSET ?
  `, [...queryParams, limit, offset]);

  // Get total count
  const totalResult = await getRow(`
    SELECT COUNT(*) as total 
    FROM users 
    ${whereClause}
  `, queryParams);

  res.status(200).json({
    status: 'success',
    data: {
      users,
      pagination: {
        page,
        limit,
        total: totalResult.total,
        pages: Math.ceil(totalResult.total / limit)
      }
    }
  });
}));

// Update user role/status
router.put('/users/:id', authorize('admin'), catchAsync(async (req, res, next) => {
  const userId = parseInt(req.params.id);
  const { role, is_active, subscription_status } = req.body;

  // Check if user exists
  const user = await getRow('SELECT * FROM users WHERE id = ?', [userId]);
  if (!user) {
    return next(new AppError('User not found', 404));
  }

  // Prevent admin from deactivating themselves
  if (userId === req.user.id && is_active === false) {
    return next(new AppError('You cannot deactivate your own account', 400));
  }

  const updates = [];
  const values = [];

  if (role && ['user', 'subscriber', 'admin', 'editor'].includes(role)) {
    updates.push('role = ?');
    values.push(role);
  }

  if (typeof is_active === 'boolean') {
    updates.push('is_active = ?');
    values.push(is_active ? 1 : 0);
  }

  if (subscription_status && ['free', 'active', 'expired'].includes(subscription_status)) {
    updates.push('subscription_status = ?');
    values.push(subscription_status);
  }

  if (updates.length === 0) {
    return next(new AppError('No valid fields to update', 400));
  }

  updates.push('updated_at = CURRENT_TIMESTAMP');
  values.push(userId);

  await runQuery(
    `UPDATE users SET ${updates.join(', ')} WHERE id = ?`,
    values
  );

  logger.info(`User updated by admin: ${user.email} by ${req.user.email}`);

  res.status(200).json({
    status: 'success',
    message: 'User updated successfully'
  });
}));

// Get all books for admin management
router.get('/books', catchAsync(async (req, res, next) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 20;
  const offset = (page - 1) * limit;

  const books = await getAllRows(`
    SELECT 
      b.*,
      u.first_name || ' ' || u.last_name as uploaded_by_name
    FROM books b
    LEFT JOIN users u ON b.uploaded_by = u.id
    ORDER BY b.upload_date DESC
    LIMIT ? OFFSET ?
  `, [limit, offset]);

  // Get total count
  const totalResult = await getRow('SELECT COUNT(*) as total FROM books');

  res.status(200).json({
    status: 'success',
    data: {
      books,
      pagination: {
        page,
        limit,
        total: totalResult.total,
        pages: Math.ceil(totalResult.total / limit)
      }
    }
  });
}));

// Upload new book (placeholder - file upload will be implemented in Phase 2)
router.post('/books', catchAsync(async (req, res, next) => {
  res.status(501).json({
    status: 'error',
    message: 'Book upload functionality will be implemented in Phase 2'
  });
}));

// Update book metadata
router.put('/books/:id', catchAsync(async (req, res, next) => {
  const bookId = parseInt(req.params.id);
  const { title, author, description, category, access_level, price, tags } = req.body;

  // Check if book exists
  const book = await getRow('SELECT * FROM books WHERE id = ?', [bookId]);
  if (!book) {
    return next(new AppError('Book not found', 404));
  }

  const updates = [];
  const values = [];

  if (title) {
    updates.push('title = ?');
    values.push(title);
  }
  if (author) {
    updates.push('author = ?');
    values.push(author);
  }
  if (description) {
    updates.push('description = ?');
    values.push(description);
  }
  if (category) {
    updates.push('category = ?');
    values.push(category);
  }
  if (access_level && ['public', 'registered', 'paid'].includes(access_level)) {
    updates.push('access_level = ?');
    values.push(access_level);
  }
  if (price !== undefined) {
    updates.push('price = ?');
    values.push(parseFloat(price));
  }
  if (tags) {
    updates.push('tags = ?');
    values.push(JSON.stringify(tags));
  }

  if (updates.length === 0) {
    return next(new AppError('No valid fields to update', 400));
  }

  values.push(bookId);

  await runQuery(
    `UPDATE books SET ${updates.join(', ')} WHERE id = ?`,
    values
  );

  logger.info(`Book updated by admin: ${book.title} by ${req.user.email}`);

  res.status(200).json({
    status: 'success',
    message: 'Book updated successfully'
  });
}));

// Delete book
router.delete('/books/:id', authorize('admin'), catchAsync(async (req, res, next) => {
  const bookId = parseInt(req.params.id);

  // Check if book exists
  const book = await getRow('SELECT * FROM books WHERE id = ?', [bookId]);
  if (!book) {
    return next(new AppError('Book not found', 404));
  }

  // Soft delete (set is_active to false)
  await runQuery('UPDATE books SET is_active = 0 WHERE id = ?', [bookId]);

  logger.info(`Book deleted by admin: ${book.title} by ${req.user.email}`);

  res.status(200).json({
    status: 'success',
    message: 'Book deleted successfully'
  });
}));

// Get payment history
router.get('/payments', catchAsync(async (req, res, next) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 20;
  const offset = (page - 1) * limit;

  const payments = await getAllRows(`
    SELECT 
      p.*,
      u.email as user_email,
      u.first_name || ' ' || u.last_name as user_name,
      b.title as book_title
    FROM payments p
    LEFT JOIN users u ON p.user_id = u.id
    LEFT JOIN books b ON p.book_id = b.id
    ORDER BY p.payment_date DESC
    LIMIT ? OFFSET ?
  `, [limit, offset]);

  // Get total count
  const totalResult = await getRow('SELECT COUNT(*) as total FROM payments');

  res.status(200).json({
    status: 'success',
    data: {
      payments,
      pagination: {
        page,
        limit,
        total: totalResult.total,
        pages: Math.ceil(totalResult.total / limit)
      }
    }
  });
}));

module.exports = router;
