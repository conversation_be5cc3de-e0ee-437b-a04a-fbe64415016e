const { runQuery, getRow, initializeDatabase } = require('../config/database');
const { hashPassword } = require('../middleware/auth');
const logger = require('../utils/logger');

async function seedDatabase() {
  try {
    logger.info('Starting database seeding...');

    // Initialize database first
    await initializeDatabase();

    // Create admin user
    const adminPassword = await hashPassword('admin123');
    await runQuery(`
      INSERT OR IGNORE INTO users (email, password_hash, first_name, last_name, role)
      VALUES (?, ?, ?, ?, ?)
    `, ['<EMAIL>', adminPassword, 'Admin', 'User', 'admin']);

    // Create test user
    const userPassword = await hashPassword('user123');
    await runQuery(`
      INSERT OR IGNORE INTO users (email, password_hash, first_name, last_name, role)
      VALUES (?, ?, ?, ?, ?)
    `, ['<EMAIL>', userPassword, 'Test', 'User', 'user']);

    // Create subscriber user
    const subscriberPassword = await hashPassword('subscriber123');
    await runQuery(`
      INSERT OR IGNORE INTO users (email, password_hash, first_name, last_name, role, subscription_status, subscription_end_date)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `, ['<EMAIL>', subscriberPassword, 'Subscriber', 'User', 'subscriber', 'active', '2024-12-31 23:59:59']);

    // Get admin user ID for book uploads
    const adminUser = await getRow('SELECT id FROM users WHERE email = ?', ['<EMAIL>']);

    // Create sample books
    const sampleBooks = [
      {
        title: 'Introduction to Programming',
        author: 'John Doe',
        description: 'A comprehensive guide to programming fundamentals',
        category: 'Technology',
        access_level: 'public',
        price: 0.00,
        tags: JSON.stringify(['programming', 'beginner', 'tutorial']),
        file_path: 'uploads/sample-programming.pdf',
        page_count: 150
      },
      {
        title: 'Advanced JavaScript Concepts',
        author: 'Jane Smith',
        description: 'Deep dive into advanced JavaScript programming techniques',
        category: 'Technology',
        access_level: 'registered',
        price: 0.00,
        tags: JSON.stringify(['javascript', 'advanced', 'web development']),
        file_path: 'uploads/sample-javascript.pdf',
        page_count: 200
      },
      {
        title: 'Machine Learning Fundamentals',
        author: 'Dr. AI Expert',
        description: 'Introduction to machine learning algorithms and applications',
        category: 'Science',
        access_level: 'paid',
        price: 29.99,
        tags: JSON.stringify(['machine learning', 'AI', 'data science']),
        file_path: 'uploads/sample-ml.pdf',
        page_count: 300
      },
      {
        title: 'Digital Marketing Strategy',
        author: 'Marketing Pro',
        description: 'Complete guide to digital marketing in the modern age',
        category: 'Business',
        access_level: 'public',
        price: 0.00,
        tags: JSON.stringify(['marketing', 'digital', 'strategy']),
        file_path: 'uploads/sample-marketing.pdf',
        page_count: 180
      },
      {
        title: 'Data Structures and Algorithms',
        author: 'Computer Scientist',
        description: 'Essential data structures and algorithms for programmers',
        category: 'Technology',
        access_level: 'registered',
        price: 0.00,
        tags: JSON.stringify(['algorithms', 'data structures', 'computer science']),
        file_path: 'uploads/sample-algorithms.pdf',
        page_count: 250
      }
    ];

    for (const book of sampleBooks) {
      await runQuery(`
        INSERT OR IGNORE INTO books (
          title, author, description, category, access_level, price, 
          tags, file_path, page_count, uploaded_by, file_size
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        book.title, book.author, book.description, book.category,
        book.access_level, book.price, book.tags, book.file_path,
        book.page_count, adminUser.id, 1024000 // 1MB sample size
      ]);
    }

    // Create sample annotations for test user
    const testUser = await getRow('SELECT id FROM users WHERE email = ?', ['<EMAIL>']);
    const firstBook = await getRow('SELECT id FROM books WHERE title = ?', ['Introduction to Programming']);

    if (testUser && firstBook) {
      // Add to user's library
      await runQuery(`
        INSERT OR IGNORE INTO user_books (user_id, book_id, downloaded_at, last_read_at, current_page, reading_progress)
        VALUES (?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 25, 16.67)
      `, [testUser.id, firstBook.id]);

      // Add sample annotations
      await runQuery(`
        INSERT OR IGNORE INTO annotations (user_id, book_id, page_number, annotation_type, content, color)
        VALUES (?, ?, ?, ?, ?, ?)
      `, [testUser.id, firstBook.id, 10, 'highlight', 'Important concept', '#FFFF00']);

      await runQuery(`
        INSERT OR IGNORE INTO annotations (user_id, book_id, page_number, annotation_type, content)
        VALUES (?, ?, ?, ?, ?)
      `, [testUser.id, firstBook.id, 15, 'note', 'Remember to practice this example']);

      await runQuery(`
        INSERT OR IGNORE INTO annotations (user_id, book_id, page_number, annotation_type, content)
        VALUES (?, ?, ?, ?, ?)
      `, [testUser.id, firstBook.id, 20, 'bookmark', 'Chapter 3 start']);
    }

    // Create sample payment records
    if (testUser) {
      await runQuery(`
        INSERT OR IGNORE INTO payments (user_id, book_id, amount, currency, payment_method, transaction_id, status)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `, [testUser.id, null, 9.99, 'USD', 'stripe', 'pi_test_123456', 'completed']);
    }

    logger.info('Database seeding completed successfully!');
    logger.info('Sample users created:');
    logger.info('  Admin: <EMAIL> / admin123');
    logger.info('  User: <EMAIL> / user123');
    logger.info('  Subscriber: <EMAIL> / subscriber123');
    logger.info('Sample books and data have been added.');

  } catch (error) {
    logger.error('Database seeding failed:', error);
    throw error;
  }
}

// Run if called directly
if (require.main === module) {
  seedDatabase()
    .then(() => {
      logger.info('Seeding completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('Seeding failed:', error);
      process.exit(1);
    });
}

module.exports = seedDatabase;
