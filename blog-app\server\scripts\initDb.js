const { initializeDatabase } = require('../config/database');
const logger = require('../utils/logger');

async function initDb() {
  try {
    logger.info('Starting database initialization...');
    
    await initializeDatabase();
    
    logger.info('Database initialization completed successfully!');
    process.exit(0);
  } catch (error) {
    logger.error('Database initialization failed:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  initDb();
}

module.exports = initDb;
