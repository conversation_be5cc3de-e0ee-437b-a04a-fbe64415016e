# Project Requirements & Methodology (PRM)

## Title

Cross-Platform PDF Reader & Downloader with Admin Management and Payments

## Goal

Develop a PDF reader/downloader app with annotation features, user access control, and a web-based admin dashboard. Supports web, Android, and iOS platforms.

---

## Functional Requirements

### User Features (All Platforms)

* View PDFs with zoom, scroll, pagination
* Download PDFs to local storage
* Highlight text, add annotations, bookmark pages
* Full-text search within PDFs
* User registration/login
* Role-based access (guest, registered, subscriber)
* View reading history & downloaded books

### Admin Features (Web Only)

* Upload/manage PDF files
* Edit book metadata (title, author, tags, category)
* Assign access levels (public, registered, paid)
* View analytics (views/downloads per PDF)
* Manage user roles (admin, editor)

### Payment Integration

* M-Pesa Daraja API (mobile payments in Kenya)
* Stripe (credit/debit cards)
* Google Play Billing (Android)
* Apple In-App Purchase (iOS)

---

## Technical Stack

### Frontend (Web)

* React.js + Vite
* TailwindCSS (UI)
* `react-pdf` for PDF rendering

### Frontend (Mobile)

* React Native + Expo
* `react-native-pdf` for PDF rendering
* NativeBase or ShadCN UI

### Backend

* Node.js + Express
* SQLite (local dev) / PostgreSQL (cloud-ready)
* JWT-based Auth
* REST API for frontend consumption
* Multer for file uploads

### Admin Panel

* React Web App
* Protected routes via admin role

### Storage

* Local file system in development
* AWS S3 or Firebase Storage for production

### Payments

* M-Pesa via Daraja API (Node.js SDK)
* Stripe Checkout integration
* Google Play Billing (via Expo in-app-purchases)
* Apple IAP (via Expo or native bridge)

---

## Project Structure

```
/pdf-reader-app
├── client-web/        # React app (web frontend)
├── client-mobile/     # React Native app (mobile frontend)
├── admin-panel/       # React admin dashboard
├── server/            # Express backend API
├── db/                # SQLite or PostgreSQL schema
└── storage/           # PDF uploads (local or S3)
```

---

## Testing Strategy

### Unit Tests

* Jest for backend logic (auth, DB, payments)
* React Testing Library for frontend components

### Integration Tests

* API endpoint testing using Supertest
* File upload & download verification
* Payment API sandbox testing (Stripe, M-Pesa)

### Manual QA

* PDF rendering (annotations/bookmarks)
* Role-based access control flows
* Payment checkout flows

---

## Debugging Guidance

* Enable detailed logs (`winston` or `debug`) for Express API
* Check mobile emulator logs for PDF rendering issues
* Use Stripe/M-Pesa sandbox environments to simulate payments
* Validate PDF metadata extraction and search indexing

---

## Deployment Prep

* Migrate SQLite to PostgreSQL if scaling
* Move local PDF storage to S3 or Firebase
* Enable HTTPS and environment variables for API secrets
* Set up mobile app signing for Play Store & App Store

---

## Dependencies (Core)

* `express`, `jsonwebtoken`, `multer`, `sqlite3`
* `react`, `react-pdf`, `react-router-dom`, `tailwindcss`
* `react-native`, `react-native-pdf`, `expo`
* `stripe`, `mpesa-node-sdk`

---

## Assumptions

* All PDFs are non-encrypted, publicly readable
* M-Pesa API credentials will be available for integration
* Apps will be manually deployed after localhost development

---

## Future Enhancements

* Bookmark sync across devices
* Push notifications (new book uploads)
* OCR for scanned PDFs
* ElasticSearch for faster in-text search
