# PDF Reader App

A cross-platform PDF reader application with annotation features, user access control, and web-based admin dashboard. Supports web, Android, and iOS platforms.

## 🚀 Features

### User Features
- View PDFs with zoom, scroll, pagination
- Download PDFs to local storage
- Highlight text, add annotations, bookmark pages
- Full-text search within PDFs
- User registration/login with role-based access
- Reading history & downloaded books tracking

### Admin Features
- Upload/manage PDF files
- Edit book metadata (title, author, tags, category)
- Assign access levels (public, registered, paid)
- View analytics (views/downloads per PDF)
- Manage user roles (admin, editor)

### Payment Integration
- M-Pesa Daraja API (mobile payments in Kenya)
- Stripe (credit/debit cards)
- Google Play Billing (Android)
- Apple In-App Purchase (iOS)

## 🏗️ Architecture

```
/pdf-reader-app
├── client-web/        # React web frontend
├── client-mobile/     # React Native mobile app
├── admin-panel/       # React admin dashboard
├── server/            # Express backend API
├── db/                # Database schema and migrations
└── storage/           # PDF file storage (local/cloud)
```

## 🛠️ Tech Stack

- **Frontend Web**: React.js + Vite + TailwindCSS + react-pdf
- **Frontend Mobile**: React Native + Expo + react-native-pdf
- **Backend**: Node.js + Express + JWT Auth
- **Database**: SQLite (dev) / PostgreSQL (prod)
- **Storage**: Local filesystem (dev) / AWS S3 (prod)
- **Payments**: Stripe, M-Pesa, Google Play, Apple IAP

## 📋 Prerequisites

- Node.js (v18+)
- npm (v8+)
- Git
- Android Studio (for mobile development)
- Xcode (for iOS development, Mac only)

## 🚀 Quick Start

### 1. Clone and Install
```bash
git clone <repository-url>
cd pdf-reader-app
npm run install:all
```

### 2. Environment Setup
```bash
# Copy environment template
cp server/.env.example server/.env

# Edit environment variables
# Add your API keys and configuration
```

### 3. Database Setup
```bash
# Initialize database
cd server
npm run db:init
npm run db:seed
```

### 4. Start Development Servers
```bash
# Start all services
npm run dev

# Or start individually:
npm run server:dev    # Backend API (http://localhost:3000)
npm run web:dev       # Web app (http://localhost:5173)
npm run admin:dev     # Admin panel (http://localhost:5174)
npm run mobile:dev    # Mobile app (Expo)
```

## 📱 Mobile Development

### Android
```bash
cd client-mobile
npx expo run:android
```

### iOS
```bash
cd client-mobile
npx expo run:ios
```

## 🧪 Testing

```bash
# Run all tests
npm test

# Run specific tests
npm run test:server
npm run test:web
```

## 🚀 Deployment

### Backend Deployment
```bash
# Build and deploy backend
cd server
npm run build
npm run start
```

### Web App Deployment
```bash
# Build web application
npm run build:web

# Deploy to hosting service
# (Vercel, Netlify, AWS, etc.)
```

### Mobile App Deployment
```bash
# Build for production
cd client-mobile
npx expo build:android
npx expo build:ios
```

## 📚 API Documentation

API documentation is available at `/api/docs` when running the development server.

### Key Endpoints
- `POST /api/auth/login` - User authentication
- `GET /api/books` - List available books
- `GET /api/books/:id/download` - Download PDF
- `POST /api/books/:id/annotations` - Save annotations
- `POST /api/admin/books` - Upload new book (admin)

## 🔧 Configuration

### Environment Variables
```env
# Server Configuration
NODE_ENV=development
PORT=3000
JWT_SECRET=your-jwt-secret

# Database
DATABASE_URL=sqlite:./database.sqlite

# File Storage
UPLOAD_PATH=./uploads
AWS_S3_BUCKET=your-s3-bucket

# Payment APIs
STRIPE_SECRET_KEY=sk_test_...
MPESA_CONSUMER_KEY=your-mpesa-key
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Check the documentation in `/docs`
- Review the troubleshooting guide

## 🗺️ Roadmap

- [x] Project setup and architecture
- [ ] Backend API development
- [ ] Web frontend development
- [ ] Admin panel development
- [ ] Mobile app development
- [ ] Payment integration
- [ ] Testing and deployment

## 📊 Project Status

Current Phase: **Phase 1 - Project Setup & Foundation**

See [development-workflow.md](../development-workflow.md) for detailed development plan and [progress-tracker.md](../progress-tracker.md) for current progress.
