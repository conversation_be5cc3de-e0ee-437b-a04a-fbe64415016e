const express = require('express');
const { authenticate } = require('../middleware/auth');
const { AppError, catchAsync } = require('../middleware/errorHandler');
const { getAllRows, getRow, runQuery } = require('../config/database');
const logger = require('../utils/logger');

const router = express.Router();

// All payment routes require authentication
router.use(authenticate);

// Get user payment history
router.get('/history', catchAsync(async (req, res, next) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 10;
  const offset = (page - 1) * limit;

  const payments = await getAllRows(`
    SELECT 
      p.*,
      b.title as book_title,
      b.author as book_author
    FROM payments p
    LEFT JOIN books b ON p.book_id = b.id
    WHERE p.user_id = ?
    ORDER BY p.payment_date DESC
    LIMIT ? OFFSET ?
  `, [req.user.id, limit, offset]);

  // Get total count
  const totalResult = await getRow(`
    SELECT COUNT(*) as total 
    FROM payments 
    WHERE user_id = ?
  `, [req.user.id]);

  res.status(200).json({
    status: 'success',
    data: {
      payments,
      pagination: {
        page,
        limit,
        total: totalResult.total,
        pages: Math.ceil(totalResult.total / limit)
      }
    }
  });
}));

// Stripe payment processing (placeholder)
router.post('/stripe', catchAsync(async (req, res, next) => {
  const { amount, currency = 'USD', bookId, paymentMethodId } = req.body;

  if (!amount || !paymentMethodId) {
    return next(new AppError('Amount and payment method are required', 400));
  }

  // TODO: Implement Stripe payment processing
  // This will be implemented in Phase 6: Payment Integration

  logger.info(`Stripe payment initiated: $${amount} by user ${req.user.email}`);

  res.status(501).json({
    status: 'error',
    message: 'Stripe payment processing will be implemented in Phase 6'
  });
}));

// M-Pesa payment processing (placeholder)
router.post('/mpesa', catchAsync(async (req, res, next) => {
  const { amount, phoneNumber, bookId } = req.body;

  if (!amount || !phoneNumber) {
    return next(new AppError('Amount and phone number are required', 400));
  }

  // TODO: Implement M-Pesa payment processing
  // This will be implemented in Phase 6: Payment Integration

  logger.info(`M-Pesa payment initiated: KES ${amount} by user ${req.user.email}`);

  res.status(501).json({
    status: 'error',
    message: 'M-Pesa payment processing will be implemented in Phase 6'
  });
}));

// Verify payment status
router.get('/verify/:transactionId', catchAsync(async (req, res, next) => {
  const { transactionId } = req.params;

  const payment = await getRow(`
    SELECT * FROM payments 
    WHERE transaction_id = ? AND user_id = ?
  `, [transactionId, req.user.id]);

  if (!payment) {
    return next(new AppError('Payment not found', 404));
  }

  res.status(200).json({
    status: 'success',
    data: {
      payment
    }
  });
}));

// Webhook endpoints for payment providers (placeholders)

// Stripe webhook
router.post('/webhooks/stripe', express.raw({ type: 'application/json' }), catchAsync(async (req, res, next) => {
  // TODO: Implement Stripe webhook handling
  // This will be implemented in Phase 6: Payment Integration
  
  logger.info('Stripe webhook received');
  
  res.status(200).json({
    status: 'success',
    message: 'Webhook received'
  });
}));

// M-Pesa callback
router.post('/callbacks/mpesa', catchAsync(async (req, res, next) => {
  // TODO: Implement M-Pesa callback handling
  // This will be implemented in Phase 6: Payment Integration
  
  logger.info('M-Pesa callback received');
  
  res.status(200).json({
    status: 'success',
    message: 'Callback received'
  });
}));

// Get subscription status
router.get('/subscription', catchAsync(async (req, res, next) => {
  const user = await getRow(`
    SELECT 
      subscription_status,
      subscription_end_date,
      role
    FROM users 
    WHERE id = ?
  `, [req.user.id]);

  // Get recent payments for subscription
  const recentPayments = await getAllRows(`
    SELECT * FROM payments 
    WHERE user_id = ? AND book_id IS NULL 
    ORDER BY payment_date DESC 
    LIMIT 5
  `, [req.user.id]);

  res.status(200).json({
    status: 'success',
    data: {
      subscription: {
        status: user.subscription_status,
        end_date: user.subscription_end_date,
        is_admin: user.role === 'admin'
      },
      recentPayments
    }
  });
}));

// Create subscription payment (placeholder)
router.post('/subscribe', catchAsync(async (req, res, next) => {
  const { plan, paymentMethod } = req.body;

  if (!plan || !paymentMethod) {
    return next(new AppError('Plan and payment method are required', 400));
  }

  // TODO: Implement subscription payment processing
  // This will be implemented in Phase 6: Payment Integration

  logger.info(`Subscription payment initiated: ${plan} by user ${req.user.email}`);

  res.status(501).json({
    status: 'error',
    message: 'Subscription payment processing will be implemented in Phase 6'
  });
}));

module.exports = router;
