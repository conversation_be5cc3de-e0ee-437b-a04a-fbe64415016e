const express = require('express');
const path = require('path');
const fs = require('fs').promises;
const { getAllRows, getRow, runQuery } = require('../config/database');
const { authenticate, optionalAuth } = require('../middleware/auth');
const { AppError, catchAsync } = require('../middleware/errorHandler');
const logger = require('../utils/logger');

const router = express.Router();

// Get all books (with optional authentication for access control)
router.get('/', optionalAuth, catchAsync(async (req, res, next) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 20;
  const offset = (page - 1) * limit;
  const category = req.query.category;
  const search = req.query.search;

  let whereClause = 'WHERE b.is_active = 1';
  let queryParams = [];

  // Add access level filtering based on user authentication
  if (!req.user) {
    whereClause += ' AND b.access_level = ?';
    queryParams.push('public');
  } else if (req.user.role === 'user') {
    whereClause += ' AND b.access_level IN (?, ?)';
    queryParams.push('public', 'registered');
  } else if (req.user.subscription_status === 'active' || req.user.role === 'admin') {
    // Subscribers and admins can see all books
  }

  // Add category filter
  if (category) {
    whereClause += ' AND b.category = ?';
    queryParams.push(category);
  }

  // Add search filter
  if (search) {
    whereClause += ' AND (b.title LIKE ? OR b.author LIKE ? OR b.description LIKE ?)';
    const searchTerm = `%${search}%`;
    queryParams.push(searchTerm, searchTerm, searchTerm);
  }

  const books = await getAllRows(`
    SELECT 
      b.id,
      b.title,
      b.author,
      b.description,
      b.category,
      b.tags,
      b.cover_image_path,
      b.page_count,
      b.access_level,
      b.price,
      b.upload_date,
      b.view_count,
      b.download_count,
      u.first_name || ' ' || u.last_name as uploaded_by_name
    FROM books b
    LEFT JOIN users u ON b.uploaded_by = u.id
    ${whereClause}
    ORDER BY b.upload_date DESC
    LIMIT ? OFFSET ?
  `, [...queryParams, limit, offset]);

  // Get total count
  const totalResult = await getRow(`
    SELECT COUNT(*) as total 
    FROM books b 
    ${whereClause}
  `, queryParams);

  res.status(200).json({
    status: 'success',
    data: {
      books,
      pagination: {
        page,
        limit,
        total: totalResult.total,
        pages: Math.ceil(totalResult.total / limit)
      }
    }
  });
}));

// Get book details
router.get('/:id', optionalAuth, catchAsync(async (req, res, next) => {
  const bookId = parseInt(req.params.id);

  const book = await getRow(`
    SELECT 
      b.*,
      u.first_name || ' ' || u.last_name as uploaded_by_name
    FROM books b
    LEFT JOIN users u ON b.uploaded_by = u.id
    WHERE b.id = ? AND b.is_active = 1
  `, [bookId]);

  if (!book) {
    return next(new AppError('Book not found', 404));
  }

  // Check access permissions
  if (book.access_level === 'registered' && !req.user) {
    return next(new AppError('Registration required to access this book', 401));
  }

  if (book.access_level === 'paid' && (!req.user || (req.user.subscription_status !== 'active' && req.user.role !== 'admin'))) {
    return next(new AppError('Subscription required to access this book', 403));
  }

  // Increment view count
  await runQuery('UPDATE books SET view_count = view_count + 1 WHERE id = ?', [bookId]);

  // Get user's reading progress if authenticated
  let userProgress = null;
  if (req.user) {
    userProgress = await getRow(`
      SELECT current_page, reading_progress, is_favorite, last_read_at
      FROM user_books 
      WHERE user_id = ? AND book_id = ?
    `, [req.user.id, bookId]);
  }

  res.status(200).json({
    status: 'success',
    data: {
      book,
      userProgress
    }
  });
}));

// Download book PDF
router.get('/:id/download', authenticate, catchAsync(async (req, res, next) => {
  const bookId = parseInt(req.params.id);

  const book = await getRow('SELECT * FROM books WHERE id = ? AND is_active = 1', [bookId]);
  
  if (!book) {
    return next(new AppError('Book not found', 404));
  }

  // Check access permissions
  if (book.access_level === 'paid' && req.user.subscription_status !== 'active' && req.user.role !== 'admin') {
    return next(new AppError('Subscription required to download this book', 403));
  }

  // Check if file exists
  const filePath = path.join(__dirname, '..', book.file_path);
  
  try {
    await fs.access(filePath);
  } catch (error) {
    logger.error(`File not found: ${filePath}`);
    return next(new AppError('File not found on server', 404));
  }

  // Update download count and user's download record
  await runQuery('UPDATE books SET download_count = download_count + 1 WHERE id = ?', [bookId]);
  
  await runQuery(`
    INSERT INTO user_books (user_id, book_id, downloaded_at)
    VALUES (?, ?, CURRENT_TIMESTAMP)
    ON CONFLICT(user_id, book_id) 
    DO UPDATE SET downloaded_at = CURRENT_TIMESTAMP
  `, [req.user.id, bookId]);

  logger.info(`Book downloaded: ${book.title} by user: ${req.user.email}`);

  // Set appropriate headers
  res.setHeader('Content-Type', 'application/pdf');
  res.setHeader('Content-Disposition', `attachment; filename="${book.title}.pdf"`);
  
  // Send file
  res.sendFile(filePath);
}));

// Search books
router.post('/search', optionalAuth, catchAsync(async (req, res, next) => {
  const { query, category, author } = req.body;
  
  if (!query || query.trim().length < 2) {
    return next(new AppError('Search query must be at least 2 characters long', 400));
  }

  let whereClause = 'WHERE b.is_active = 1';
  let queryParams = [];

  // Add access level filtering
  if (!req.user) {
    whereClause += ' AND b.access_level = ?';
    queryParams.push('public');
  } else if (req.user.role === 'user') {
    whereClause += ' AND b.access_level IN (?, ?)';
    queryParams.push('public', 'registered');
  }

  // Add search conditions
  whereClause += ' AND (b.title LIKE ? OR b.author LIKE ? OR b.description LIKE ?)';
  const searchTerm = `%${query.trim()}%`;
  queryParams.push(searchTerm, searchTerm, searchTerm);

  // Add optional filters
  if (category) {
    whereClause += ' AND b.category = ?';
    queryParams.push(category);
  }

  if (author) {
    whereClause += ' AND b.author LIKE ?';
    queryParams.push(`%${author}%`);
  }

  const books = await getAllRows(`
    SELECT 
      b.id,
      b.title,
      b.author,
      b.description,
      b.category,
      b.cover_image_path,
      b.page_count,
      b.access_level,
      b.price
    FROM books b
    ${whereClause}
    ORDER BY b.view_count DESC, b.title ASC
    LIMIT 50
  `, queryParams);

  res.status(200).json({
    status: 'success',
    data: {
      books,
      query,
      total: books.length
    }
  });
}));

// Get book categories
router.get('/meta/categories', catchAsync(async (req, res, next) => {
  const categories = await getAllRows(`
    SELECT 
      category,
      COUNT(*) as book_count
    FROM books 
    WHERE is_active = 1 AND category IS NOT NULL
    GROUP BY category
    ORDER BY book_count DESC, category ASC
  `);

  res.status(200).json({
    status: 'success',
    data: {
      categories
    }
  });
}));

module.exports = router;
