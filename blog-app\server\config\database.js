const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs').promises;
const logger = require('../utils/logger');

let db = null;

// Database configuration
const dbConfig = {
  filename: process.env.DB_PATH || path.join(__dirname, '../database.sqlite'),
  mode: sqlite3.OPEN_READWRITE | sqlite3.OPEN_CREATE
};

// Initialize database connection
async function initializeDatabase() {
  try {
    // Ensure database directory exists
    const dbDir = path.dirname(dbConfig.filename);
    await fs.mkdir(dbDir, { recursive: true });

    // Create database connection
    db = new sqlite3.Database(dbConfig.filename, dbConfig.mode, (err) => {
      if (err) {
        logger.error('Error opening database:', err);
        throw err;
      }
      logger.info(`Connected to SQLite database: ${dbConfig.filename}`);
    });

    // Enable foreign keys
    await runQuery('PRAGMA foreign_keys = ON');
    
    // Create tables
    await createTables();
    
    logger.info('Database initialization completed');
    return db;
  } catch (error) {
    logger.error('Database initialization failed:', error);
    throw error;
  }
}

// Create database tables
async function createTables() {
  const tables = [
    // Users table
    `CREATE TABLE IF NOT EXISTS users (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      email VARCHAR(255) UNIQUE NOT NULL,
      password_hash VARCHAR(255) NOT NULL,
      first_name VARCHAR(100),
      last_name VARCHAR(100),
      role TEXT CHECK(role IN ('guest', 'user', 'subscriber', 'admin', 'editor')) DEFAULT 'user',
      subscription_status TEXT CHECK(subscription_status IN ('free', 'active', 'expired')) DEFAULT 'free',
      subscription_end_date DATETIME,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      last_login DATETIME,
      is_active BOOLEAN DEFAULT 1
    )`,

    // Books table
    `CREATE TABLE IF NOT EXISTS books (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      title VARCHAR(255) NOT NULL,
      author VARCHAR(255),
      description TEXT,
      file_path VARCHAR(500) NOT NULL,
      file_size INTEGER,
      page_count INTEGER,
      access_level TEXT CHECK(access_level IN ('public', 'registered', 'paid')) DEFAULT 'public',
      price DECIMAL(10,2) DEFAULT 0.00,
      category VARCHAR(100),
      tags TEXT,
      cover_image_path VARCHAR(500),
      upload_date DATETIME DEFAULT CURRENT_TIMESTAMP,
      uploaded_by INTEGER REFERENCES users(id),
      download_count INTEGER DEFAULT 0,
      view_count INTEGER DEFAULT 0,
      is_active BOOLEAN DEFAULT 1,
      metadata TEXT
    )`,

    // User_books table (reading history)
    `CREATE TABLE IF NOT EXISTS user_books (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
      book_id INTEGER REFERENCES books(id) ON DELETE CASCADE,
      downloaded_at DATETIME,
      last_read_at DATETIME,
      current_page INTEGER DEFAULT 1,
      reading_progress DECIMAL(5,2) DEFAULT 0.00,
      is_favorite BOOLEAN DEFAULT 0,
      UNIQUE(user_id, book_id)
    )`,

    // Annotations table
    `CREATE TABLE IF NOT EXISTS annotations (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
      book_id INTEGER REFERENCES books(id) ON DELETE CASCADE,
      page_number INTEGER NOT NULL,
      annotation_type TEXT CHECK(annotation_type IN ('highlight', 'note', 'bookmark')) NOT NULL,
      content TEXT,
      position_data TEXT,
      color VARCHAR(7) DEFAULT '#FFFF00',
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )`,

    // Payments table
    `CREATE TABLE IF NOT EXISTS payments (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
      book_id INTEGER REFERENCES books(id) ON DELETE SET NULL,
      amount DECIMAL(10,2) NOT NULL,
      currency VARCHAR(3) DEFAULT 'USD',
      payment_method TEXT CHECK(payment_method IN ('stripe', 'mpesa', 'google_play', 'apple_iap')),
      transaction_id VARCHAR(255) UNIQUE,
      status TEXT CHECK(status IN ('pending', 'completed', 'failed', 'refunded')) DEFAULT 'pending',
      payment_date DATETIME DEFAULT CURRENT_TIMESTAMP,
      metadata TEXT
    )`
  ];

  // Create indexes
  const indexes = [
    'CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)',
    'CREATE INDEX IF NOT EXISTS idx_users_role ON users(role)',
    'CREATE INDEX IF NOT EXISTS idx_books_access_level ON books(access_level)',
    'CREATE INDEX IF NOT EXISTS idx_books_category ON books(category)',
    'CREATE INDEX IF NOT EXISTS idx_user_books_user_id ON user_books(user_id)',
    'CREATE INDEX IF NOT EXISTS idx_user_books_book_id ON user_books(book_id)',
    'CREATE INDEX IF NOT EXISTS idx_annotations_user_book ON annotations(user_id, book_id)',
    'CREATE INDEX IF NOT EXISTS idx_payments_user_id ON payments(user_id)',
    'CREATE INDEX IF NOT EXISTS idx_payments_status ON payments(status)'
  ];

  try {
    // Create tables
    for (const tableSQL of tables) {
      await runQuery(tableSQL);
    }
    
    // Create indexes
    for (const indexSQL of indexes) {
      await runQuery(indexSQL);
    }
    
    logger.info('All database tables and indexes created successfully');
  } catch (error) {
    logger.error('Error creating tables:', error);
    throw error;
  }
}

// Helper function to run SQL queries
function runQuery(sql, params = []) {
  return new Promise((resolve, reject) => {
    if (!db) {
      reject(new Error('Database not initialized'));
      return;
    }

    db.run(sql, params, function(err) {
      if (err) {
        logger.error('SQL Error:', err);
        reject(err);
      } else {
        resolve({ id: this.lastID, changes: this.changes });
      }
    });
  });
}

// Helper function to get single row
function getRow(sql, params = []) {
  return new Promise((resolve, reject) => {
    if (!db) {
      reject(new Error('Database not initialized'));
      return;
    }

    db.get(sql, params, (err, row) => {
      if (err) {
        logger.error('SQL Error:', err);
        reject(err);
      } else {
        resolve(row);
      }
    });
  });
}

// Helper function to get all rows
function getAllRows(sql, params = []) {
  return new Promise((resolve, reject) => {
    if (!db) {
      reject(new Error('Database not initialized'));
      return;
    }

    db.all(sql, params, (err, rows) => {
      if (err) {
        logger.error('SQL Error:', err);
        reject(err);
      } else {
        resolve(rows);
      }
    });
  });
}

// Close database connection
function closeDatabase() {
  return new Promise((resolve, reject) => {
    if (db) {
      db.close((err) => {
        if (err) {
          logger.error('Error closing database:', err);
          reject(err);
        } else {
          logger.info('Database connection closed');
          resolve();
        }
      });
    } else {
      resolve();
    }
  });
}

module.exports = {
  initializeDatabase,
  runQuery,
  getRow,
  getAllRows,
  closeDatabase,
  getDb: () => db
};
